import aiohttp
from rate_limiter import RateLimiter

class MexcClient:
    BASE_URL = "https://contract.mexc.com/api/v1/contract"

    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        self.limiter = RateLimiter()

    async def get_contracts(self):
        await self.limiter.acquire("contracts", 1, 5.0)
        async with self.session.get(f"{self.BASE_URL}/detail") as resp:
            data = await resp.json()
            return data.get("data", [])

    async def get_depth(self, symbol: str):
        await self.limiter.acquire("depth", 20, 2.0)
        async with self.session.get(f"{self.BASE_URL}/depth/{symbol}") as resp:
            return await resp.json()

    async def get_funding_rate(self, symbol: str):
        await self.limiter.acquire("funding", 20, 2.0)
        async with self.session.get(f"{self.BASE_URL}/funding_rate/{symbol}") as resp:
            data = await resp.json()
            return data.get("data", {})

    async def get_deals(self, symbol: str, limit: int = 100):
        await self.limiter.acquire("deals", 20, 2.0)
        async with self.session.get(f"{self.BASE_URL}/deals/{symbol}", params={"limit": limit}) as resp:
            data = await resp.json()
            return data.get("data", [])
