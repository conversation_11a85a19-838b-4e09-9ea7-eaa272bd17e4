import statistics
from dataclasses import dataclass
from typing import List
from config import (
    HIGH_VOLATILITY_THRESHOLD,
    FUNDING_DIVERGENCE_THRESHOLD,
    ORDERBOOK_IMBALANCE_THRESHOLD,
    LIQUIDATION_VOLUME_MULTIPLIER,
    VOLUME_SURGE_MULTIPLIER,
    EXTREME_FUNDING_THRESHOLD,
    LOW_VOLATILITY_THRESHOLD
)

@dataclass
class Signal:
    symbol: str
    score: float
    volatility: float
    trend: str
    funding_rate: float
    volume_24h: float
    imbalance: float
    signals: List[str]

def calculate_volatility(deals: list) -> float:
    if not deals:
        return 0.0
    prices = [d["p"] for d in deals]
    return statistics.stdev(prices) / statistics.mean(prices) if len(prices) > 1 else 0.0

def calculate_trend(deals: list) -> tuple[str, float]:
    if len(deals) < 10:
        return "neutral", 0.0

    prices = [d["p"] for d in deals[-20:]]
    trend_strength = (prices[-1] - prices[0]) / prices[0]

    if trend_strength > 0.002:
        return "up", trend_strength
    elif trend_strength < -0.002:
        return "down", trend_strength
    return "neutral", trend_strength

def calculate_orderbook_imbalance(depth: dict) -> float:
    asks = depth.get("asks", [])
    bids = depth.get("bids", [])

    if not asks or not bids:
        return 0.0

    bid_vol = sum(b[1] for b in bids[:10])
    ask_vol = sum(a[1] for a in asks[:10])
    total = bid_vol + ask_vol

    return (bid_vol - ask_vol) / total if total > 0 else 0.0

def detect_liquidation_cascade(deals: list) -> bool:
    if len(deals) < 5:
        return False

    recent = deals[-5:]
    volumes = [d["v"] for d in recent]
    avg_vol = statistics.mean(volumes)

    return max(volumes) > avg_vol * LIQUIDATION_VOLUME_MULTIPLIER

def analyze_pair(symbol: str, depth: dict, funding: dict, deals: list) -> Signal:
    volatility = calculate_volatility(deals)
    trend, trend_strength = calculate_trend(deals)
    funding_rate = funding.get("fundingRate", 0.0)
    imbalance = calculate_orderbook_imbalance(depth)
    volume_24h = sum(d["v"] for d in deals)

    signals = []
    score = 0.0

    # High volatility
    if volatility > HIGH_VOLATILITY_THRESHOLD:
        signals.append("High Vol")
        score += volatility * 100

    # Trend-funding divergence
    if trend == "up" and funding_rate < -FUNDING_DIVERGENCE_THRESHOLD:
        signals.append("Up+NegFund")
        score += abs(funding_rate) * 10000
    elif trend == "down" and funding_rate > FUNDING_DIVERGENCE_THRESHOLD:
        signals.append("Down+PosFund")
        score += funding_rate * 10000

    # Liquidation cascade
    if detect_liquidation_cascade(deals):
        signals.append("Liq Cascade")
        score += 20

    # Strong orderbook imbalance
    if abs(imbalance) > ORDERBOOK_IMBALANCE_THRESHOLD:
        signals.append(f"OB Imbal {'Buy' if imbalance > 0 else 'Sell'}")
        score += abs(imbalance) * 30

    # Momentum divergence (price vs volume)
    if len(deals) > 10:
        recent_vol = sum(d["v"] for d in deals[-10:])
        older_vol = sum(d["v"] for d in deals[-20:-10]) if len(deals) >= 20 else recent_vol
        if older_vol > 0 and recent_vol / older_vol > VOLUME_SURGE_MULTIPLIER:
            signals.append("Vol Surge")
            score += 15

    # Extreme funding with low volatility (squeeze setup)
    if abs(funding_rate) > EXTREME_FUNDING_THRESHOLD and volatility < LOW_VOLATILITY_THRESHOLD:
        signals.append("Squeeze Setup")
        score += 25

    return Signal(
        symbol=symbol,
        score=score,
        volatility=volatility,
        trend=trend,
        funding_rate=funding_rate,
        volume_24h=volume_24h,
        imbalance=imbalance,
        signals=signals
    )
