import asyncio
import aiohttp
from mexc_client import MexcClient
from signals import analyze_pair
from display import display_signals
from config import MIN_VOLUME, REFRESH_INTERVAL, MAX_PAIRS_CHECK

async def process_pair(client: MexcClient, symbol: str):
    try:
        depth, funding, deals = await asyncio.gather(
            client.get_depth(symbol),
            client.get_funding_rate(symbol),
            client.get_deals(symbol)
        )
        return analyze_pair(symbol, depth, funding, deals)
    except Exception as e:
        return None

async def main():
    async with aiohttp.ClientSession() as session:
        client = MexcClient(session)

        while True:
            try:
                contracts = await client.get_contracts()

                # Filter active USDT contracts
                active = [c for c in contracts if c.get("state") == 0 and c.get("settleCoin") == "USDT"]

                # Quick volume check with initial deals fetch
                volume_checks = await asyncio.gather(*[client.get_deals(c["symbol"]) for c in active[:MAX_PAIRS_CHECK]])

                pairs_to_process = []
                for contract, deals in zip(active[:MAX_PAIRS_CHECK], volume_checks):
                    volume = sum(d["v"] * d["p"] for d in deals) if deals else 0
                    if volume >= MIN_VOLUME:
                        pairs_to_process.append(contract["symbol"])

                # Process pairs in batches to respect rate limits
                batch_size = 10
                all_signals = []

                for i in range(0, len(pairs_to_process), batch_size):
                    batch = pairs_to_process[i:i + batch_size]
                    signals = await asyncio.gather(*[process_pair(client, symbol) for symbol in batch])
                    all_signals.extend([s for s in signals if s])
                    await asyncio.sleep(0.5)  # Small delay between batches

                display_signals(all_signals)
                await asyncio.sleep(REFRESH_INTERVAL)

            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
                await asyncio.sleep(5)

if __name__ == "__main__":
    asyncio.run(main())
