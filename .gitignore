

# Byte-compiled / optimized / DLL files

__pycache__/
*.py[cod]
*$py.class

# C extensions

*.so

# Distribution / packaging

.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs

pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports

htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations

*.mo
*.pot

# Django stuff:

*.log

# Sphinx documentation

docs/_build/

# PyCharm
#  More info: https://www.jetbrains.com/help/pycharm/creating-and-sharing-project-metadata.html
.idea/
*.iml
*.ipr
*.iws
out/
