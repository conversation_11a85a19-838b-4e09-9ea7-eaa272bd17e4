# TradeFinder - MEXC Futures Signal Scanner

A lightweight, high-performance scanner for MEXC futures markets that identifies interesting trading opportunities in real-time.

## Features

- **Real-time monitoring** of MEXC perpetual futures
- **Smart signal detection**:
  - High volatility pairs
  - Trend-funding divergence (counter-trend funding)
  - Liquidation cascades
  - Orderbook imbalances
  - Volume surges
  - Squeeze setups (extreme funding + low volatility)
- **Elegant rate limiting** respecting MEXC API limits
- **Color-coded console output** for quick analysis
- **Configurable filters** to reduce noise

## Installation

```bash
# Install dependencies
pip install aiohttp aiosqlite
```

## Usage

```bash
python main.py
```

The scanner will continuously fetch data and display the top trading opportunities ranked by signal strength.

## Configuration

Edit `config.py` to adjust:

- `MIN_VOLUME` - Minimum 24h volume filter (default: 10000 USD)
- `REFRESH_INTERVAL` - Display refresh rate (default: 5 seconds)
- `TOP_N_DISPLAY` - Number of pairs to show (default: 15)
- Signal detection thresholds

## Output

The display shows:
- **Symbol** - Trading pair
- **Score** - Signal strength (higher = more interesting)
- **Vol%** - Price volatility percentage
- **Trend** - Direction indicator (▲ up, ▼ down, ● neutral)
- **Fund** - Funding rate (green = negative, red = positive)
- **Signals** - Active signal types

## Architecture

- `rate_limiter.py` - Efficient rate limiting for API calls
- `mexc_client.py` - Async MEXC API client
- `signals.py` - Signal detection and scoring algorithms
- `display.py` - Terminal UI with colors
- `config.py` - Centralized configuration
- `main.py` - Main event loop

## API Rate Limits

The scanner respects MEXC rate limits:
- Contract detail: 1 call / 5 seconds
- Depth, funding, deals: 20 calls / 2 seconds

Total lines of code: ~250
