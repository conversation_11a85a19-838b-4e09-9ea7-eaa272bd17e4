[project]
name = "tradefinder"
version = "0.1.0"
description = ""
authors = [
    {name = "bram",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiosqlite (>=0.21.0,<0.22.0)",
    "aiohttp (>=3.12.15,<4.0.0)"
]

[tool.poetry]
packages = [{include = "tradefinder", from = "src"}]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
