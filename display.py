from signals import Signal
from config import TOP_N_DISPLAY

class Colors:
    RESET = "\033[0m"
    BOLD = "\033[1m"
    RED = "\033[91m"
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    BLUE = "\033[94m"
    MAGENTA = "\033[95m"
    CYAN = "\033[96m"
    WHITE = "\033[97m"

def clear_screen():
    print("\033[2J\033[H", end="")

def format_funding(rate: float) -> str:
    color = Colors.GREEN if rate < 0 else Colors.RED
    return f"{color}{rate*100:+.4f}%{Colors.RESET}"

def format_trend(trend: str) -> str:
    if trend == "up":
        return f"{Colors.GREEN}▲{Colors.RESET}"
    elif trend == "down":
        return f"{Colors.RED}▼{Colors.RESET}"
    return f"{Colors.YELLOW}●{Colors.RESET}"

def format_signals(signals: list) -> str:
    return f"{Colors.CYAN}{', '.join(signals)}{Colors.RESET}" if signals else ""

def display_signals(signals: list[Signal], top_n: int = TOP_N_DISPLAY):
    clear_screen()
    print(f"{Colors.BOLD}{Colors.CYAN}╔═══════════════════════════════════════════════════════════════════════════╗{Colors.RESET}")
    print(f"{Colors.BOLD}{Colors.CYAN}║                          MEXC FUTURES SCANNER                             ║{Colors.RESET}")
    print(f"{Colors.BOLD}{Colors.CYAN}╚═══════════════════════════════════════════════════════════════════════════╝{Colors.RESET}\n")

    sorted_signals = sorted(signals, key=lambda x: x.score, reverse=True)[:top_n]

    if not sorted_signals:
        print(f"{Colors.YELLOW}No signals detected. Waiting for data...{Colors.RESET}")
        return

    header = f"{Colors.BOLD}{'Symbol':<15} {'Score':<8} {'Vol%':<8} {' ':<2} {'Fund':<10} {'Signals'}{Colors.RESET}"
    print(header)
    print(f"{Colors.WHITE}{'─' * 100}{Colors.RESET}")

    for sig in sorted_signals:
        if sig.score < 1:
            continue

        symbol_display = f"{Colors.WHITE}{sig.symbol:<15}{Colors.RESET}"
        score_color = Colors.MAGENTA if sig.score > 50 else Colors.YELLOW if sig.score > 20 else Colors.WHITE
        score_display = f"{score_color}{sig.score:<8.1f}{Colors.RESET}"
        vol_display = f"{Colors.YELLOW}{sig.volatility*100:<7.2f}%{Colors.RESET}"
        trend_display = format_trend(sig.trend)
        funding_display = format_funding(sig.funding_rate)
        signals_display = format_signals(sig.signals)

        print(f"{symbol_display} {score_display} {vol_display} {trend_display}  {funding_display:<20} {signals_display}")

    print(f"\n{Colors.WHITE}Monitoring {len(signals)} pairs{Colors.RESET}")
