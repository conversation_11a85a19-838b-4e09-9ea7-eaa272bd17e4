import asyncio
from collections import defaultdict
from time import time

class RateLimiter:
    def __init__(self):
        self.calls = defaultdict(list)

    async def acquire(self, key: str, max_calls: int, period: float):
        now = time()
        self.calls[key] = [t for t in self.calls[key] if now - t < period]

        if len(self.calls[key]) >= max_calls:
            sleep_time = period - (now - self.calls[key][0])
            await asyncio.sleep(sleep_time)
            self.calls[key] = self.calls[key][1:]

        self.calls[key].append(time())
